import 'package:flutter/material.dart';
import '../services/employee_service.dart';
import 'employee_list_screen.dart';
import 'add_employee_screen.dart';
import 'statistics_screen.dart';
import 'salary_calculator_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final EmployeeService _employeeService = EmployeeService.instance;
  int _totalEmployees = 0;
  int _eligibleForPromotion = 0;
  int _eligibleForAllowance = 0;
  double _totalSalaryBudget = 0.0;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    final employees = await _employeeService.loadEmployees();
    final promotionEligible = await _employeeService
        .getEmployeesEligibleForPromotion();
    final allowanceEligible = await _employeeService
        .getEmployeesEligibleForAllowance();

    double totalBudget = 0;
    for (final employee in employees) {
      totalBudget += employee.totalSalary;
    }

    setState(() {
      _totalEmployees = employees.length;
      _eligibleForPromotion = promotionEligible.length;
      _eligibleForAllowance = allowanceEligible.length;
      _totalSalaryBudget = totalBudget;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام إدارة الموظفين'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadStatistics,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة الترحيب
              _buildWelcomeCard(),

              const SizedBox(height: 20),

              // إحصائيات سريعة
              _buildQuickStats(),

              const SizedBox(height: 20),

              // الوظائف الرئيسية
              _buildMainFunctions(),

              const SizedBox(height: 20),

              // التنبيهات والإشعارات
              _buildNotifications(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مرحباً بك في نظام إدارة الموظفين',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'نظام شامل لإدارة شؤون الموظفين وحساب الرواتب والترقيات',
              style: TextStyle(fontSize: 16, color: Colors.white70),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.people, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'إجمالي الموظفين: $_totalEmployees',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات سريعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'المستحقون للترقية',
                _eligibleForPromotion.toString(),
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'المستحقون للعلاوة',
                _eligibleForAllowance.toString(),
                Icons.attach_money,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildStatCard(
          'إجمالي ميزانية الرواتب',
          '${_totalSalaryBudget.toStringAsFixed(0)} د.ع',
          Icons.account_balance_wallet,
          Colors.blue,
          isWide: true,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    bool isWide = false,
  }) {
    return Card(
      child: Container(
        width: isWide ? double.infinity : null,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainFunctions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الوظائف الرئيسية',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildFunctionCard(
              'إضافة موظف',
              Icons.person_add,
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddEmployeeScreen(),
                ),
              ).then((_) => _loadStatistics()),
            ),
            _buildFunctionCard(
              'قائمة الموظفين',
              Icons.people,
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EmployeeListScreen(),
                ),
              ).then((_) => _loadStatistics()),
            ),
            _buildFunctionCard(
              'حاسبة الراتب',
              Icons.calculate,
              Colors.orange,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SalaryCalculatorScreen(),
                ),
              ),
            ),
            _buildFunctionCard(
              'الإحصائيات',
              Icons.analytics,
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatisticsScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFunctionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 48, color: color),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotifications() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التنبيهات والإشعارات',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (_eligibleForPromotion > 0)
          _buildNotificationCard(
            'يوجد $_eligibleForPromotion موظف مستحق للترقية',
            Icons.trending_up,
            Colors.green,
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    const EmployeeListScreen(filterType: 'promotion'),
              ),
            ),
          ),
        if (_eligibleForAllowance > 0)
          _buildNotificationCard(
            'يوجد $_eligibleForAllowance موظف مستحق للعلاوة السنوية',
            Icons.attach_money,
            Colors.orange,
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    const EmployeeListScreen(filterType: 'allowance'),
              ),
            ),
          ),
        if (_eligibleForPromotion == 0 && _eligibleForAllowance == 0)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 12),
                  Text('لا توجد تنبيهات جديدة'),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNotificationCard(
    String message,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: 12),
              Expanded(
                child: Text(message, style: const TextStyle(fontSize: 16)),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
