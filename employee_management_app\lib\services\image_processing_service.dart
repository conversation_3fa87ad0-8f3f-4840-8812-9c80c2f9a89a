import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageProcessingService {
  static ImageProcessingService? _instance;
  final ImagePicker _picker = ImagePicker();

  ImageProcessingService._internal();

  static ImageProcessingService get instance {
    _instance ??= ImageProcessingService._internal();
    return _instance!;
  }

  // اختيار صورة من المعرض
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      print('خطأ في اختيار الصورة من المعرض: $e');
      return null;
    }
  }

  // التقاط صورة بالكاميرا
  Future<File?> captureImageWithCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      print('خطأ في التقاط الصورة بالكاميرا: $e');
      return null;
    }
  }

  // حفظ الصورة في مجلد التطبيق
  Future<String?> saveImageToAppDirectory(File imageFile) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String imagesDir = path.join(appDir.path, 'employee_images');

      // إنشاء مجلد الصور إذا لم يكن موجوداً
      final Directory imagesDirObj = Directory(imagesDir);
      if (!await imagesDirObj.exists()) {
        await imagesDirObj.create(recursive: true);
      }

      // إنشاء اسم فريد للصورة
      final String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String savedPath = path.join(imagesDir, fileName);

      // نسخ الصورة إلى المجلد الجديد
      final File savedImage = await imageFile.copy(savedPath);

      return savedImage.path;
    } catch (e) {
      print('خطأ في حفظ الصورة: $e');
      return null;
    }
  }

  // حذف صورة من مجلد التطبيق
  Future<bool> deleteImage(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        await imageFile.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في حذف الصورة: $e');
      return false;
    }
  }

  // التحقق من وجود الصورة
  Future<bool> imageExists(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      return await imageFile.exists();
    } catch (e) {
      return false;
    }
  }

  // الحصول على حجم الصورة
  Future<int?> getImageSize(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        return await imageFile.length();
      }
      return null;
    } catch (e) {
      print('خطأ في الحصول على حجم الصورة: $e');
      return null;
    }
  }

  // تنظيف الصور غير المستخدمة
  Future<void> cleanupUnusedImages(List<String> usedImagePaths) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String imagesDir = path.join(appDir.path, 'employee_images');
      final Directory imagesDirObj = Directory(imagesDir);

      if (await imagesDirObj.exists()) {
        final List<FileSystemEntity> files = await imagesDirObj.list().toList();

        for (final file in files) {
          if (file is File) {
            final String filePath = file.path;
            if (!usedImagePaths.contains(filePath)) {
              await file.delete();
              print('تم حذف الصورة غير المستخدمة: $filePath');
            }
          }
        }
      }
    } catch (e) {
      print('خطأ في تنظيف الصور غير المستخدمة: $e');
    }
  }

  // استخراج النص من الصورة (OCR) - يتطلب مكتبة خارجية
  Future<Map<String, String>?> extractTextFromImage(File imageFile) async {
    try {
      // هذه دالة تحاكي استخراج النص من الصورة
      // في التطبيق الحقيقي، يمكن استخدام مكتبات مثل google_ml_kit أو firebase_ml_vision

      // مثال على البيانات المستخرجة
      return {
        'name': 'اسم الموظف المستخرج',
        'nationalId': '123456789012',
        'birthDate': '1990-01-01',
        'address': 'العنوان المستخرج',
      };
    } catch (e) {
      print('خطأ في استخراج النص من الصورة: $e');
      return null;
    }
  }

  // معالجة صورة الهوية الشخصية
  Future<Map<String, String>?> processIdCard(File imageFile) async {
    try {
      // معالجة خاصة لصور الهوية الشخصية
      final extractedData = await extractTextFromImage(imageFile);

      if (extractedData != null) {
        // تنظيف وتنسيق البيانات المستخرجة
        return {
          'name': _cleanExtractedText(extractedData['name'] ?? ''),
          'nationalId': _cleanNationalId(extractedData['nationalId'] ?? ''),
          'birthDate': _cleanDate(extractedData['birthDate'] ?? ''),
          'address': _cleanExtractedText(extractedData['address'] ?? ''),
        };
      }

      return null;
    } catch (e) {
      print('خطأ في معالجة صورة الهوية: $e');
      return null;
    }
  }

  // تنظيف النص المستخرج
  String _cleanExtractedText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  // تنظيف الرقم الوطني
  String _cleanNationalId(String nationalId) {
    return nationalId.replaceAll(RegExp(r'[^\d]'), '');
  }

  // تنظيف التاريخ
  String _cleanDate(String date) {
    // محاولة تحويل التاريخ إلى تنسيق صحيح
    final RegExp dateRegex = RegExp(r'(\d{4})-(\d{2})-(\d{2})');
    final match = dateRegex.firstMatch(date);

    if (match != null) {
      return '${match.group(1)}-${match.group(2)}-${match.group(3)}';
    }

    return date;
  }

  // ضغط الصورة
  Future<File?> compressImage(File imageFile, {int quality = 85}) async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final String fileName =
          '${DateTime.now().millisecondsSinceEpoch}_compressed.jpg';
      final String compressedPath = path.join(tempDir.path, fileName);

      // هنا يمكن استخدام مكتبة ضغط الصور مثل flutter_image_compress
      // للبساطة، سنقوم بنسخ الملف فقط
      final File compressedFile = await imageFile.copy(compressedPath);

      return compressedFile;
    } catch (e) {
      print('خطأ في ضغط الصورة: $e');
      return null;
    }
  }

  // تحويل الصورة إلى Base64
  Future<String?> imageToBase64(File imageFile) async {
    try {
      final Uint8List imageBytes = await imageFile.readAsBytes();
      return base64Encode(imageBytes);
    } catch (e) {
      print('خطأ في تحويل الصورة إلى Base64: $e');
      return null;
    }
  }

  // تحويل Base64 إلى صورة
  Future<File?> base64ToImage(String base64String, String fileName) async {
    try {
      final Uint8List imageBytes = base64Decode(base64String);
      final Directory tempDir = await getTemporaryDirectory();
      final String filePath = path.join(tempDir.path, fileName);

      final File imageFile = File(filePath);
      await imageFile.writeAsBytes(imageBytes);

      return imageFile;
    } catch (e) {
      print('خطأ في تحويل Base64 إلى صورة: $e');
      return null;
    }
  }
}
