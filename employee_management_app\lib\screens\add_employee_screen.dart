import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import '../models/employee.dart';
import '../models/salary_scale.dart';
import '../services/employee_service.dart';
import '../services/image_processing_service.dart';

class AddEmployeeScreen extends StatefulWidget {
  final Employee? employee; // للتعديل

  const AddEmployeeScreen({super.key, this.employee});

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeeService _employeeService = EmployeeService.instance;
  final ImageProcessingService _imageService = ImageProcessingService.instance;

  // Controllers
  final _nameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();

  // Variables
  String? _selectedJobTitle;
  int _selectedJobGrade = 9;
  int _selectedJobStep = 1;
  String _selectedDepartment = '';
  String _selectedPosition = '';
  String _selectedEducationLevel = '';
  String _selectedSpecialization = '';
  String _selectedMaritalStatus = 'أعزب';
  int _numberOfChildren = 0;
  DateTime _selectedHireDate = DateTime.now();
  DateTime _selectedBirthDate = DateTime.now().subtract(
    const Duration(days: 365 * 25),
  );
  File? _selectedImage;
  String? _imagePath;

  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.employee != null;
    if (_isEditing) {
      _populateFields();
    }
  }

  void _populateFields() {
    final employee = widget.employee!;
    _nameController.text = employee.name;
    _nationalIdController.text = employee.nationalId;
    _addressController.text = employee.address;
    _phoneController.text = employee.phoneNumber;
    _emailController.text = employee.email;

    _selectedJobTitle = employee.jobTitle;
    _selectedJobGrade = employee.jobGrade;
    _selectedJobStep = employee.jobStep;
    _selectedDepartment = employee.department;
    _selectedPosition = employee.position;
    _selectedEducationLevel = employee.educationLevel;
    _selectedSpecialization = employee.specialization;
    _selectedMaritalStatus = employee.maritalStatus;
    _numberOfChildren = employee.numberOfChildren;
    _selectedHireDate = employee.hireDate;
    _selectedBirthDate = employee.birthDate;
    _imagePath = employee.imagePath;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nationalIdController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _selectImage() async {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () async {
                Navigator.pop(context);
                final image = await _imageService.captureImageWithCamera();
                if (image != null) {
                  setState(() => _selectedImage = image);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () async {
                Navigator.pop(context);
                final image = await _imageService.pickImageFromGallery();
                if (image != null) {
                  setState(() => _selectedImage = image);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.document_scanner),
              title: const Text('مسح الهوية الشخصية'),
              onTap: () async {
                Navigator.pop(context);
                await _scanIdCard();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _scanIdCard() async {
    final image = await _imageService.captureImageWithCamera();
    if (image != null) {
      setState(() => _isLoading = true);

      try {
        final extractedData = await _imageService.processIdCard(image);
        if (extractedData != null && mounted) {
          // ملء الحقول بالبيانات المستخرجة
          _nameController.text = extractedData['name'] ?? '';
          _nationalIdController.text = extractedData['nationalId'] ?? '';
          _addressController.text = extractedData['address'] ?? '';

          if (extractedData['birthDate'] != null) {
            try {
              _selectedBirthDate = DateTime.parse(extractedData['birthDate']!);
            } catch (e) {
              // تجاهل خطأ تحويل التاريخ
            }
          }

          setState(() => _selectedImage = image);

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم استخراج البيانات من الهوية بنجاح'),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في معالجة الصورة: $e')));
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isHireDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isHireDate ? _selectedHireDate : _selectedBirthDate,
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isHireDate) {
          _selectedHireDate = picked;
        } else {
          _selectedBirthDate = picked;
        }
      });
    }
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // التحقق من عدم تكرار الرقم الوطني
      final existingEmployee = await _employeeService.findEmployeeByNationalId(
        _nationalIdController.text,
      );
      if (existingEmployee != null &&
          (!_isEditing || existingEmployee.id != widget.employee!.id)) {
        throw Exception('الرقم الوطني موجود مسبقاً');
      }

      // حفظ الصورة
      String? savedImagePath;
      if (_selectedImage != null) {
        savedImagePath = await _imageService.saveImageToAppDirectory(
          _selectedImage!,
        );
      } else if (_isEditing) {
        savedImagePath = _imagePath;
      }

      // إنشاء كائن الموظف
      final employee = Employee(
        id: _isEditing
            ? widget.employee!.id
            : await _employeeService.generateNewEmployeeId(),
        name: _nameController.text.trim(),
        nationalId: _nationalIdController.text.trim(),
        jobTitle: _selectedJobTitle ?? '',
        jobGrade: _selectedJobGrade,
        jobStep: _selectedJobStep,
        hireDate: _selectedHireDate,
        birthDate: _selectedBirthDate,
        department: _selectedDepartment,
        position: _selectedPosition,
        basicSalary: SalaryScale.getBasicSalary(
          _selectedJobGrade,
          _selectedJobStep,
        ),
        educationLevel: _selectedEducationLevel,
        specialization: _selectedSpecialization,
        maritalStatus: _selectedMaritalStatus,
        numberOfChildren: _numberOfChildren,
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        imagePath: savedImagePath,
      );

      // حفظ أو تحديث الموظف
      if (_isEditing) {
        await _employeeService.updateEmployee(employee);
      } else {
        await _employeeService.addEmployee(employee);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing
                  ? 'تم تحديث بيانات الموظف بنجاح'
                  : 'تم إضافة الموظف بنجاح',
            ),
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'),
        centerTitle: true,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveEmployee,
              tooltip: 'حفظ',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة الموظف
              _buildImageSection(),

              const SizedBox(height: 24),

              // المعلومات الشخصية
              _buildPersonalInfoSection(),

              const SizedBox(height: 24),

              // المعلومات الوظيفية
              _buildJobInfoSection(),

              const SizedBox(height: 24),

              // معلومات الاتصال
              _buildContactInfoSection(),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'صورة الموظف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GestureDetector(
              onTap: _selectImage,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey[300]!, width: 2),
                  color: Colors.grey[100],
                ),
                child: _selectedImage != null
                    ? ClipOval(
                        child: Image.file(
                          _selectedImage!,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                        ),
                      )
                    : _imagePath != null
                    ? ClipOval(
                        child: Image.file(
                          File(_imagePath!),
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.person_add_alt_1,
                              size: 48,
                              color: Colors.grey,
                            );
                          },
                        ),
                      )
                    : const Icon(
                        Icons.person_add_alt_1,
                        size: 48,
                        color: Colors.grey,
                      ),
              ),
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: _selectImage,
              icon: const Icon(Icons.camera_alt),
              label: const Text('اختيار صورة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الشخصية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // الاسم الكامل
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم الكامل *',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // الرقم الوطني
            TextFormField(
              controller: _nationalIdController,
              decoration: const InputDecoration(
                labelText: 'الرقم الوطني *',
                prefixIcon: Icon(Icons.badge),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(12),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الرقم الوطني';
                }
                if (value.length != 12) {
                  return 'الرقم الوطني يجب أن يكون 12 رقم';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // تاريخ الولادة
            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ الولادة *',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_selectedBirthDate.day}/${_selectedBirthDate.month}/${_selectedBirthDate.year}',
                ),
              ),
            ),

            const SizedBox(height: 16),

            // الحالة الاجتماعية
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'الحالة الاجتماعية *',
                prefixIcon: Icon(Icons.family_restroom),
              ),
              value: _selectedMaritalStatus,
              items: ['أعزب', 'متزوج', 'مطلق', 'أرمل'].map((status) {
                return DropdownMenuItem(value: status, child: Text(status));
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedMaritalStatus = value!);
              },
            ),

            const SizedBox(height: 16),

            // عدد الأطفال
            TextFormField(
              initialValue: _numberOfChildren.toString(),
              decoration: const InputDecoration(
                labelText: 'عدد الأطفال',
                prefixIcon: Icon(Icons.child_care),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              onChanged: (value) {
                _numberOfChildren = int.tryParse(value) ?? 0;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الوظيفية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // المنصب الوظيفي
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'المنصب الوظيفي *',
                prefixIcon: Icon(Icons.work),
              ),
              value: _selectedJobTitle,
              items: JobTitles.getAllTitles().map((title) {
                return DropdownMenuItem(value: title, child: Text(title));
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedJobTitle = value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المنصب الوظيفي';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // الدرجة الوظيفية
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'الدرجة الوظيفية *',
                      prefixIcon: Icon(Icons.grade),
                    ),
                    value: _selectedJobGrade,
                    items: SalaryScale.getAvailableGrades().map((grade) {
                      return DropdownMenuItem(
                        value: grade,
                        child: Text('الدرجة $grade'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedJobGrade = value!;
                        _selectedJobStep = 1; // إعادة تعيين المرحلة
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'المرحلة *',
                      prefixIcon: Icon(Icons.stairs),
                    ),
                    value: _selectedJobStep,
                    items: SalaryScale.getAvailableSteps(_selectedJobGrade).map(
                      (step) {
                        return DropdownMenuItem(
                          value: step,
                          child: Text('المرحلة $step'),
                        );
                      },
                    ).toList(),
                    onChanged: (value) {
                      setState(() => _selectedJobStep = value!);
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // القسم
            TextFormField(
              initialValue: _selectedDepartment,
              decoration: const InputDecoration(
                labelText: 'القسم *',
                prefixIcon: Icon(Icons.business),
              ),
              onChanged: (value) => _selectedDepartment = value,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال القسم';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // المنصب
            TextFormField(
              initialValue: _selectedPosition,
              decoration: const InputDecoration(
                labelText: 'المنصب',
                prefixIcon: Icon(Icons.assignment_ind),
              ),
              onChanged: (value) => _selectedPosition = value,
            ),

            const SizedBox(height: 16),

            // تاريخ التعيين
            InkWell(
              onTap: () => _selectDate(context, true),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ التعيين *',
                  prefixIcon: Icon(Icons.date_range),
                ),
                child: Text(
                  '${_selectedHireDate.day}/${_selectedHireDate.month}/${_selectedHireDate.year}',
                ),
              ),
            ),

            const SizedBox(height: 16),

            // المستوى التعليمي
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'المستوى التعليمي *',
                prefixIcon: Icon(Icons.school),
              ),
              value: _selectedEducationLevel.isEmpty
                  ? null
                  : _selectedEducationLevel,
              items:
                  [
                    'ابتدائية',
                    'متوسطة',
                    'إعدادية',
                    'دبلوم',
                    'بكالوريوس',
                    'ماجستير',
                    'دكتوراه',
                  ].map((level) {
                    return DropdownMenuItem(value: level, child: Text(level));
                  }).toList(),
              onChanged: (value) {
                setState(() => _selectedEducationLevel = value ?? '');
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المستوى التعليمي';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // التخصص
            TextFormField(
              initialValue: _selectedSpecialization,
              decoration: const InputDecoration(
                labelText: 'التخصص',
                prefixIcon: Icon(Icons.psychology),
              ),
              onChanged: (value) => _selectedSpecialization = value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاتصال',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // العنوان
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان *',
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // البريد الإلكتروني
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'يرجى إدخال بريد إلكتروني صحيح';
                  }
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveEmployee,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(_isEditing ? 'تحديث' : 'حفظ'),
          ),
        ),
      ],
    );
  }
}
