import 'package:flutter/material.dart';
import '../models/salary_scale.dart';

class SalaryCalculatorScreen extends StatefulWidget {
  const SalaryCalculatorScreen({super.key});

  @override
  State<SalaryCalculatorScreen> createState() => _SalaryCalculatorScreenState();
}

class _SalaryCalculatorScreenState extends State<SalaryCalculatorScreen> {
  int _selectedGrade = 9;
  int _selectedStep = 1;
  String _selectedEducationLevel = 'بكالوريوس';
  String _selectedMaritalStatus = 'أعزب';
  int _numberOfChildren = 0;

  double _basicSalary = 0;
  double _totalSalary = 0;
  Map<String, double> _allowances = {};

  @override
  void initState() {
    super.initState();
    _calculateSalary();
  }

  void _calculateSalary() {
    _basicSalary = SalaryScale.getBasicSalary(_selectedGrade, _selectedStep);

    _allowances = {
      'علاوة الشهادة': _getEducationAllowance(),
      'علاوة الزواج': _selectedMaritalStatus == 'متزوج' ? 50000 : 0,
      'علاوة الأطفال': _numberOfChildren * 25000.0,
    };

    _totalSalary = _basicSalary + _allowances.values.reduce((a, b) => a + b);

    setState(() {});
  }

  double _getEducationAllowance() {
    switch (_selectedEducationLevel) {
      case 'دكتوراه':
        return 200000;
      case 'ماجستير':
        return 150000;
      case 'بكالوريوس':
        return 100000;
      case 'دبلوم':
        return 75000;
      default:
        return 50000;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('حاسبة الراتب'), centerTitle: true),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الإعدادات
            _buildSettingsCard(),

            const SizedBox(height: 20),

            // بطاقة النتائج
            _buildResultsCard(),

            const SizedBox(height: 20),

            // بطاقة تفاصيل العلاوات
            _buildAllowancesCard(),

            const SizedBox(height: 20),

            // بطاقة مقارنة الدرجات
            _buildComparisonCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الحساب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // الدرجة والمرحلة
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'الدرجة الوظيفية',
                      prefixIcon: Icon(Icons.grade),
                    ),
                    value: _selectedGrade,
                    items: SalaryScale.getAvailableGrades().map((grade) {
                      return DropdownMenuItem(
                        value: grade,
                        child: Text('الدرجة $grade'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedGrade = value!;
                        _selectedStep = 1; // إعادة تعيين المرحلة
                      });
                      _calculateSalary();
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'المرحلة',
                      prefixIcon: Icon(Icons.stairs),
                    ),
                    value: _selectedStep,
                    items: SalaryScale.getAvailableSteps(_selectedGrade).map((
                      step,
                    ) {
                      return DropdownMenuItem(
                        value: step,
                        child: Text('المرحلة $step'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedStep = value!);
                      _calculateSalary();
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // المستوى التعليمي
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'المستوى التعليمي',
                prefixIcon: Icon(Icons.school),
              ),
              value: _selectedEducationLevel,
              items:
                  [
                    'ابتدائية',
                    'متوسطة',
                    'إعدادية',
                    'دبلوم',
                    'بكالوريوس',
                    'ماجستير',
                    'دكتوراه',
                  ].map((level) {
                    return DropdownMenuItem(value: level, child: Text(level));
                  }).toList(),
              onChanged: (value) {
                setState(() => _selectedEducationLevel = value!);
                _calculateSalary();
              },
            ),

            const SizedBox(height: 16),

            // الحالة الاجتماعية
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'الحالة الاجتماعية',
                prefixIcon: Icon(Icons.family_restroom),
              ),
              value: _selectedMaritalStatus,
              items: ['أعزب', 'متزوج', 'مطلق', 'أرمل'].map((status) {
                return DropdownMenuItem(value: status, child: Text(status));
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedMaritalStatus = value!);
                _calculateSalary();
              },
            ),

            const SizedBox(height: 16),

            // عدد الأطفال
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'عدد الأطفال:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
                IconButton(
                  onPressed: _numberOfChildren > 0
                      ? () {
                          setState(() => _numberOfChildren--);
                          _calculateSalary();
                        }
                      : null,
                  icon: const Icon(Icons.remove_circle_outline),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '$_numberOfChildren',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() => _numberOfChildren++);
                    _calculateSalary();
                  },
                  icon: const Icon(Icons.add_circle_outline),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsCard() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            const Text(
              'نتيجة الحساب',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),

            // الراتب الأساسي
            _buildResultRow('الراتب الأساسي', _basicSalary),

            const SizedBox(height: 8),

            // إجمالي العلاوات
            _buildResultRow(
              'إجمالي العلاوات',
              _allowances.values.reduce((a, b) => a + b),
            ),

            const SizedBox(height: 12),
            const Divider(color: Colors.white54),
            const SizedBox(height: 12),

            // الراتب الإجمالي
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الراتب الإجمالي',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '${_totalSalary.toStringAsFixed(0)} د.ع',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مقارنة الدرجات والمراحل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // المرحلة التالية
            if (_selectedStep < 11) ...[
              _buildComparisonRow(
                'المرحلة التالية (${_selectedStep + 1})',
                SalaryScale.getBasicSalary(_selectedGrade, _selectedStep + 1),
                SalaryScale.getStepIncrease(_selectedGrade, _selectedStep),
                Colors.blue,
              ),
            ],

            // الدرجة التالية
            if (_selectedGrade > 1) ...[
              _buildComparisonRow(
                'الدرجة التالية (${_selectedGrade - 1})',
                SalaryScale.getBasicSalary(_selectedGrade - 1, 1),
                SalaryScale.getBasicSalary(_selectedGrade - 1, 1) -
                    _basicSalary,
                Colors.green,
              ),
            ],

            // أعلى راتب في النظام
            _buildComparisonRow(
              'أعلى راتب (الدرجة 1 - المرحلة 11)',
              SalaryScale.getBasicSalary(1, 11),
              SalaryScale.getBasicSalary(1, 11) - _basicSalary,
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonRow(
    String title,
    double salary,
    double difference,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الراتب الأساسي:', style: const TextStyle(fontSize: 14)),
              Text(
                '${salary.toStringAsFixed(0)} د.ع',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الفرق:', style: const TextStyle(fontSize: 14)),
              Text(
                '${difference > 0 ? '+' : ''}${difference.toStringAsFixed(0)} د.ع',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: difference > 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultRow(String label, double amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 16, color: Colors.white70),
        ),
        Text(
          '${amount.toStringAsFixed(0)} د.ع',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildAllowancesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل العلاوات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ..._allowances.entries.map((entry) {
              if (entry.value > 0) {
                return _buildAllowanceRow(entry.key, entry.value);
              }
              return const SizedBox.shrink();
            }).toList(),

            if (_allowances.values.every((value) => value == 0))
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'لا توجد علاوات إضافية',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllowanceRow(String label, double amount) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 16)),
          Text(
            '${amount.toStringAsFixed(0)} د.ع',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }
}
