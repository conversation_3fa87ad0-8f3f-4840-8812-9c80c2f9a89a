import 'package:flutter/material.dart';
import 'dart:io';
import '../models/employee.dart';
import '../models/salary_scale.dart';
import '../services/employee_service.dart';
import 'add_employee_screen.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({super.key, required this.employee});

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  final EmployeeService _employeeService = EmployeeService.instance;
  late Employee _employee;

  @override
  void initState() {
    super.initState();
    _employee = widget.employee;
  }

  Future<void> _deleteEmployee() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${_employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _employeeService.deleteEmployee(_employee.id);
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف الموظف بنجاح')));
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في حذف الموظف: $e')));
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_employee.name),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AddEmployeeScreen(employee: _employee),
                ),
              ).then((result) {
                if (result == true) {
                  // إعادة تحميل بيانات الموظف
                  _reloadEmployee();
                }
              });
            },
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteEmployee,
            tooltip: 'حذف',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الصورة والمعلومات الأساسية
            _buildProfileCard(),

            const SizedBox(height: 20),

            // المعلومات الشخصية
            _buildPersonalInfoCard(),

            const SizedBox(height: 20),

            // المعلومات الوظيفية
            _buildJobInfoCard(),

            const SizedBox(height: 20),

            // معلومات الراتب
            _buildSalaryInfoCard(),

            const SizedBox(height: 20),

            // معلومات الترقية والعلاوة
            _buildPromotionInfoCard(),

            const SizedBox(height: 20),

            // معلومات الاتصال
            _buildContactInfoCard(),
          ],
        ),
      ),
    );
  }

  Future<void> _reloadEmployee() async {
    try {
      final employees = await _employeeService.loadEmployees();
      final updatedEmployee = employees.firstWhere((e) => e.id == _employee.id);
      setState(() {
        _employee = updatedEmployee;
      });
    } catch (e) {
      // الموظف قد يكون محذوف
      if (mounted) {
        Navigator.pop(context, true);
      }
    }
  }

  Widget _buildProfileCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // صورة الموظف
            CircleAvatar(
              radius: 60,
              backgroundColor: Theme.of(context).primaryColor,
              child: _employee.imagePath != null
                  ? ClipOval(
                      child: Image.file(
                        File(_employee.imagePath!),
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Text(
                            _employee.name.isNotEmpty ? _employee.name[0] : 'م',
                            style: const TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    )
                  : Text(
                      _employee.name.isNotEmpty ? _employee.name[0] : 'م',
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),

            const SizedBox(height: 16),

            // اسم الموظف
            Text(
              _employee.name,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // المنصب والقسم
            Text(
              _employee.jobTitle,
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 4),

            Text(
              _employee.department,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // معلومات سريعة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickInfo(
                  'سنوات الخدمة',
                  '${_employee.yearsOfService}',
                  Icons.work_history,
                ),
                _buildQuickInfo('الدرجة', '${_employee.jobGrade}', Icons.grade),
                _buildQuickInfo(
                  'المرحلة',
                  '${_employee.jobStep}',
                  Icons.stairs,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickInfo(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Widget _buildPersonalInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الشخصية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildInfoRow('الرقم الوطني', _employee.nationalId, Icons.badge),
            _buildInfoRow(
              'تاريخ الولادة',
              '${_employee.birthDate.day}/${_employee.birthDate.month}/${_employee.birthDate.year}',
              Icons.calendar_today,
            ),
            _buildInfoRow(
              'الحالة الاجتماعية',
              _employee.maritalStatus,
              Icons.family_restroom,
            ),
            _buildInfoRow(
              'عدد الأطفال',
              '${_employee.numberOfChildren}',
              Icons.child_care,
            ),
            _buildInfoRow(
              'المستوى التعليمي',
              _employee.educationLevel,
              Icons.school,
            ),
            if (_employee.specialization.isNotEmpty)
              _buildInfoRow(
                'التخصص',
                _employee.specialization,
                Icons.psychology,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الوظيفية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildInfoRow('المنصب الوظيفي', _employee.jobTitle, Icons.work),
            _buildInfoRow('القسم', _employee.department, Icons.business),
            if (_employee.position.isNotEmpty)
              _buildInfoRow('المنصب', _employee.position, Icons.assignment_ind),
            _buildInfoRow(
              'الدرجة الوظيفية',
              'الدرجة ${_employee.jobGrade}',
              Icons.grade,
            ),
            _buildInfoRow(
              'المرحلة',
              'المرحلة ${_employee.jobStep}',
              Icons.stairs,
            ),
            _buildInfoRow(
              'تاريخ التعيين',
              '${_employee.hireDate.day}/${_employee.hireDate.month}/${_employee.hireDate.year}',
              Icons.date_range,
            ),
            _buildInfoRow(
              'سنوات الخدمة',
              '${_employee.yearsOfService} سنة',
              Icons.work_history,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الراتب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildInfoRow(
              'الراتب الأساسي',
              '${_employee.basicSalary.toStringAsFixed(0)} د.ع',
              Icons.attach_money,
            ),
            _buildInfoRow(
              'الراتب الإجمالي',
              '${_employee.totalSalary.toStringAsFixed(0)} د.ع',
              Icons.account_balance_wallet,
            ),

            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 12),

            const Text(
              'تفاصيل العلاوات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),

            // علاوة الشهادة
            _buildAllowanceRow('علاوة الشهادة', _getEducationAllowance()),

            // علاوة الزواج
            if (_employee.maritalStatus == 'متزوج')
              _buildAllowanceRow('علاوة الزواج', 50000),

            // علاوة الأطفال
            if (_employee.numberOfChildren > 0)
              _buildAllowanceRow(
                'علاوة الأطفال (${_employee.numberOfChildren})',
                _employee.numberOfChildren * 25000,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromotionInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الترقية والعلاوة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // معلومات الترقية
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.trending_up, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'الترقية القادمة',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'التاريخ المتوقع: ${_employee.nextPromotionDate.day}/${_employee.nextPromotionDate.month}/${_employee.nextPromotionDate.year}',
                  ),
                  Text(
                    'المدة المتبقية: ${_employee.monthsToNextPromotion} شهر',
                  ),
                  if (_employee.jobStep < 11) ...[
                    Text(
                      'الراتب بعد الترقية: ${SalaryScale.getNextStepSalary(_employee.jobGrade, _employee.jobStep).toStringAsFixed(0)} د.ع',
                    ),
                    Text(
                      'الزيادة المتوقعة: ${SalaryScale.getStepIncrease(_employee.jobGrade, _employee.jobStep).toStringAsFixed(0)} د.ع',
                    ),
                  ] else if (_employee.jobGrade > 1) ...[
                    Text(
                      'الراتب بعد الترقية: ${SalaryScale.getNextGradeSalary(_employee.jobGrade, _employee.jobStep).toStringAsFixed(0)} د.ع',
                    ),
                    Text(
                      'الزيادة المتوقعة: ${SalaryScale.getGradeIncrease(_employee.jobGrade, _employee.jobStep).toStringAsFixed(0)} د.ع',
                    ),
                  ] else ...[
                    const Text('وصل إلى أعلى درجة ومرحلة'),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // معلومات العلاوة السنوية
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.attach_money, color: Colors.green),
                      const SizedBox(width: 8),
                      const Text(
                        'العلاوة السنوية',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'التاريخ المتوقع: ${_employee.nextAllowanceDate.day}/${_employee.nextAllowanceDate.month}/${_employee.nextAllowanceDate.year}',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاتصال',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildInfoRow('العنوان', _employee.address, Icons.location_on),
            _buildInfoRow('رقم الهاتف', _employee.phoneNumber, Icons.phone),
            if (_employee.email.isNotEmpty)
              _buildInfoRow('البريد الإلكتروني', _employee.email, Icons.email),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllowanceRow(String label, double amount) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            '${amount.toStringAsFixed(0)} د.ع',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  double _getEducationAllowance() {
    switch (_employee.educationLevel) {
      case 'دكتوراه':
        return 200000;
      case 'ماجستير':
        return 150000;
      case 'بكالوريوس':
        return 100000;
      case 'دبلوم':
        return 75000;
      default:
        return 50000;
    }
  }
}
