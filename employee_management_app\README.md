# نظام إدارة الموظفين - Employee Management System

تطبيق Flutter احترافي لإدارة شؤون الموظفين وحساب الرواتب والترقيات حسب القانون العراقي.

## المميزات الرئيسية

### 🏠 الشاشة الرئيسية
- عرض إحصائيات سريعة للموظفين
- إجمالي عدد الموظفين
- المستحقون للترقية والعلاوة
- إجمالي ميزانية الرواتب
- تنبيهات وإشعارات مهمة

### 👥 إدارة الموظفين
- إضافة موظف جديد مع جميع البيانات المطلوبة
- تعديل بيانات الموظفين الموجودين
- حذف الموظفين
- البحث والتصفية المتقدمة
- عرض تفاصيل شاملة لكل موظف

### 📊 نظام الرواتب
- حساب الراتب الأساسي حسب الدرجة والمرحلة
- حساب العلاوات التلقائية:
  - علاوة الشهادة (حسب المستوى التعليمي)
  - علاوة الزواج (50,000 د.ع)
  - علاوة الأطفال (25,000 د.ع لكل طفل)
- عرض الراتب الإجمالي
- حاسبة الراتب التفاعلية

### 📈 الترقيات والعلاوات
- حساب تاريخ الترقية القادمة
- حساب الزيادة المتوقعة في الراتب
- تتبع سنوات الخدمة
- إشعارات للمستحقين للترقية

### 📷 معالجة الصور
- إضافة صورة شخصية للموظف
- التقاط صورة بالكاميرا
- اختيار صورة من المعرض
- مسح الهوية الشخصية واستخراج البيانات (OCR)

### 📊 الإحصائيات والتقارير
- إحصائيات عامة شاملة
- توزيع الموظفين حسب الأقسام
- توزيع الموظفين حسب الدرجات الوظيفية
- توزيع الموظفين حسب نطاقات الرواتب
- توزيع الموظفين حسب المستوى التعليمي
- توزيع الموظفين حسب سنوات الخدمة

## جدول الرواتب

يعتمد التطبيق على جدول الرواتب الرسمي العراقي مع 9 درجات وظيفية و11 مرحلة لكل درجة:

| الدرجة | المرحلة 1 | المرحلة 2 | ... | المرحلة 11 |
|---------|-----------|-----------|-----|-------------|
| الأولى   | 723,000   | 740,000   | ... | 893,000     |
| الثانية  | 500,000   | 510,000   | ... | 600,000     |
| ...     | ...       | ...       | ... | ...         |
| التاسعة  | 170,000   | 173,000   | ... | 200,000     |

## المتطلبات التقنية

- Flutter SDK 3.9.2 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو iOS للاختبار

## التبعيات المستخدمة

```yaml
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.2  # لحفظ البيانات محلياً
  image_picker: ^1.0.4        # لاختيار ومعالجة الصور
  path_provider: ^2.1.1       # للوصول إلى مجلدات النظام
  path: ^1.8.3               # لمعالجة مسارات الملفات
```

## كيفية التشغيل

1. **استنساخ المشروع:**
   ```bash
   git clone [repository-url]
   cd employee_management_app
   ```

2. **تحميل التبعيات:**
   ```bash
   flutter pub get
   ```

3. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

## هيكل المشروع

```
lib/
├── main.dart                    # نقطة البداية
├── models/                      # نماذج البيانات
│   ├── employee.dart           # نموذج الموظف
│   └── salary_scale.dart       # جدول الرواتب
├── services/                    # الخدمات
│   ├── employee_service.dart   # خدمة إدارة الموظفين
│   └── image_processing_service.dart # خدمة معالجة الصور
└── screens/                     # الشاشات
    ├── home_screen.dart        # الشاشة الرئيسية
    ├── add_employee_screen.dart # إضافة/تعديل موظف
    ├── employee_list_screen.dart # قائمة الموظفين
    ├── employee_details_screen.dart # تفاصيل الموظف
    ├── salary_calculator_screen.dart # حاسبة الراتب
    └── statistics_screen.dart   # الإحصائيات
```

## الميزات المستقبلية

- [ ] تصدير التقارير إلى PDF
- [ ] نسخ احتياطية للبيانات
- [ ] تزامن البيانات مع الخادم
- [ ] إشعارات push للترقيات
- [ ] تقارير مالية متقدمة
- [ ] دعم اللغة الإنجليزية
- [ ] طباعة كشوف الرواتب

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى:
- فتح issue في GitHub
- التواصل عبر البريد الإلكتروني

## لقطات الشاشة

### الشاشة الرئيسية
- عرض الإحصائيات السريعة
- الوظائف الرئيسية
- التنبيهات والإشعارات

### إدارة الموظفين
- نموذج إضافة موظف شامل
- قائمة الموظفين مع البحث
- تفاصيل الموظف الكاملة

### حاسبة الراتب
- حساب تفاعلي للراتب
- مقارنة الدرجات والمراحل
- تفاصيل العلاوات

### الإحصائيات
- رسوم بيانية تفاعلية
- توزيعات مختلفة
- تقارير شاملة

---

**تم تطوير هذا التطبيق بواسطة:** [اسم المطور]
**تاريخ الإنشاء:** 2025
**الإصدار:** 1.0.0
