import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/employee_service.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final EmployeeService _employeeService = EmployeeService.instance;

  bool _isLoading = true;
  Map<String, dynamic> _statistics = {};
  List<Employee> _employees = [];

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);

    try {
      _employees = await _employeeService.loadEmployees();
      _statistics = await _employeeService.getEmployeeStatistics();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الإحصائيات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإحصائيات والتقارير'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadStatistics,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // إحصائيات عامة
                    _buildGeneralStatistics(),

                    const SizedBox(height: 20),

                    // إحصائيات الأقسام
                    _buildDepartmentStatistics(),

                    const SizedBox(height: 20),

                    // إحصائيات الدرجات الوظيفية
                    _buildGradeStatistics(),

                    const SizedBox(height: 20),

                    // إحصائيات الرواتب
                    _buildSalaryStatistics(),

                    const SizedBox(height: 20),

                    // إحصائيات التعليم
                    _buildEducationStatistics(),

                    const SizedBox(height: 20),

                    // إحصائيات الخدمة
                    _buildServiceStatistics(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildGeneralStatistics() {
    final totalEmployees = _statistics['totalEmployees'] ?? 0;
    final averageSalary = _statistics['averageSalary'] ?? 0.0;
    final totalBudget = _statistics['totalSalaryBudget'] ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات عامة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الموظفين',
                    totalEmployees.toString(),
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'متوسط الراتب',
                    '${averageSalary.toStringAsFixed(0)} د.ع',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            _buildStatCard(
              'إجمالي ميزانية الرواتب',
              '${totalBudget.toStringAsFixed(0)} د.ع',
              Icons.account_balance_wallet,
              Colors.purple,
              isWide: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentStatistics() {
    final departmentCounts =
        _statistics['departmentCounts'] as Map<String, int>? ?? {};

    if (departmentCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب الأقسام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...departmentCounts.entries.map((entry) {
              final percentage =
                  (entry.value / (_statistics['totalEmployees'] ?? 1)) * 100;
              return _buildProgressRow(
                entry.key,
                entry.value,
                percentage,
                Colors.blue,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildGradeStatistics() {
    final gradeCounts = _statistics['gradeCounts'] as Map<int, int>? ?? {};

    if (gradeCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب الدرجات الوظيفية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...gradeCounts.entries.map((entry) {
              final percentage =
                  (entry.value / (_statistics['totalEmployees'] ?? 1)) * 100;
              return _buildProgressRow(
                'الدرجة ${entry.key}',
                entry.value,
                percentage,
                Colors.orange,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryStatistics() {
    if (_employees.isEmpty) return const SizedBox.shrink();

    // تجميع الموظفين حسب نطاقات الرواتب
    final salaryRanges = <String, int>{
      'أقل من 500,000': 0,
      '500,000 - 750,000': 0,
      '750,000 - 1,000,000': 0,
      'أكثر من 1,000,000': 0,
    };

    for (final employee in _employees) {
      final salary = employee.totalSalary;
      if (salary < 500000) {
        salaryRanges['أقل من 500,000'] = salaryRanges['أقل من 500,000']! + 1;
      } else if (salary < 750000) {
        salaryRanges['500,000 - 750,000'] =
            salaryRanges['500,000 - 750,000']! + 1;
      } else if (salary < 1000000) {
        salaryRanges['750,000 - 1,000,000'] =
            salaryRanges['750,000 - 1,000,000']! + 1;
      } else {
        salaryRanges['أكثر من 1,000,000'] =
            salaryRanges['أكثر من 1,000,000']! + 1;
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب نطاقات الرواتب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...salaryRanges.entries.map((entry) {
              final percentage = (entry.value / _employees.length) * 100;
              return _buildProgressRow(
                entry.key,
                entry.value,
                percentage,
                Colors.green,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationStatistics() {
    if (_employees.isEmpty) return const SizedBox.shrink();

    final educationCounts = <String, int>{};
    for (final employee in _employees) {
      educationCounts[employee.educationLevel] =
          (educationCounts[employee.educationLevel] ?? 0) + 1;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب المستوى التعليمي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...educationCounts.entries.map((entry) {
              final percentage = (entry.value / _employees.length) * 100;
              return _buildProgressRow(
                entry.key,
                entry.value,
                percentage,
                Colors.purple,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatistics() {
    if (_employees.isEmpty) return const SizedBox.shrink();

    final serviceRanges = <String, int>{
      'أقل من 5 سنوات': 0,
      '5 - 10 سنوات': 0,
      '10 - 20 سنة': 0,
      'أكثر من 20 سنة': 0,
    };

    for (final employee in _employees) {
      final years = employee.yearsOfService;
      if (years < 5) {
        serviceRanges['أقل من 5 سنوات'] = serviceRanges['أقل من 5 سنوات']! + 1;
      } else if (years < 10) {
        serviceRanges['5 - 10 سنوات'] = serviceRanges['5 - 10 سنوات']! + 1;
      } else if (years < 20) {
        serviceRanges['10 - 20 سنة'] = serviceRanges['10 - 20 سنة']! + 1;
      } else {
        serviceRanges['أكثر من 20 سنة'] = serviceRanges['أكثر من 20 سنة']! + 1;
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الموظفين حسب سنوات الخدمة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ...serviceRanges.entries.map((entry) {
              final percentage = (entry.value / _employees.length) * 100;
              return _buildProgressRow(
                entry.key,
                entry.value,
                percentage,
                Colors.teal,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    bool isWide = false,
  }) {
    return Card(
      child: Container(
        width: isWide ? double.infinity : null,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressRow(
    String label,
    int count,
    double percentage,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }
}
