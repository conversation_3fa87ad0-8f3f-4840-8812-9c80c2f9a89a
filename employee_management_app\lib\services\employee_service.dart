import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';

class EmployeeService {
  static const String _employeesKey = 'employees';
  static EmployeeService? _instance;

  EmployeeService._internal();

  static EmployeeService get instance {
    _instance ??= EmployeeService._internal();
    return _instance!;
  }

  // حفظ قائمة الموظفين
  Future<void> saveEmployees(List<Employee> employees) async {
    final prefs = await SharedPreferences.getInstance();
    final employeesJson = employees.map((e) => e.toJson()).toList();
    await prefs.setString(_employeesKey, jsonEncode(employeesJson));
  }

  // تحميل قائمة الموظفين
  Future<List<Employee>> loadEmployees() async {
    final prefs = await SharedPreferences.getInstance();
    final employeesString = prefs.getString(_employeesKey);

    if (employeesString == null) {
      return [];
    }

    try {
      final employeesJson = jsonDecode(employeesString) as List;
      return employeesJson.map((json) => Employee.fromJson(json)).toList();
    } catch (e) {
      print('خطأ في تحميل بيانات الموظفين: $e');
      return [];
    }
  }

  // إضافة موظف جديد
  Future<void> addEmployee(Employee employee) async {
    final employees = await loadEmployees();
    employees.add(employee);
    await saveEmployees(employees);
  }

  // تحديث بيانات موظف
  Future<void> updateEmployee(Employee updatedEmployee) async {
    final employees = await loadEmployees();
    final index = employees.indexWhere((e) => e.id == updatedEmployee.id);

    if (index != -1) {
      employees[index] = updatedEmployee;
      await saveEmployees(employees);
    }
  }

  // حذف موظف
  Future<void> deleteEmployee(String employeeId) async {
    final employees = await loadEmployees();
    employees.removeWhere((e) => e.id == employeeId);
    await saveEmployees(employees);
  }

  // البحث عن موظف بالرقم الوطني
  Future<Employee?> findEmployeeByNationalId(String nationalId) async {
    final employees = await loadEmployees();
    try {
      return employees.firstWhere((e) => e.nationalId == nationalId);
    } catch (e) {
      return null;
    }
  }

  // البحث عن موظف بالاسم
  Future<List<Employee>> searchEmployeesByName(String name) async {
    final employees = await loadEmployees();
    return employees
        .where((e) => e.name.toLowerCase().contains(name.toLowerCase()))
        .toList();
  }

  // الحصول على الموظفين حسب القسم
  Future<List<Employee>> getEmployeesByDepartment(String department) async {
    final employees = await loadEmployees();
    return employees.where((e) => e.department == department).toList();
  }

  // الحصول على الموظفين المستحقين للترقية
  Future<List<Employee>> getEmployeesEligibleForPromotion() async {
    final employees = await loadEmployees();
    return employees.where((e) => e.monthsToNextPromotion <= 3).toList();
  }

  // الحصول على الموظفين المستحقين للعلاوة
  Future<List<Employee>> getEmployeesEligibleForAllowance() async {
    final employees = await loadEmployees();
    final now = DateTime.now();
    return employees.where((e) {
      final daysSinceHire = now.difference(e.hireDate).inDays;
      return daysSinceHire >= 365 && (daysSinceHire % 365) <= 30;
    }).toList();
  }

  // إحصائيات الموظفين
  Future<Map<String, dynamic>> getEmployeeStatistics() async {
    final employees = await loadEmployees();

    if (employees.isEmpty) {
      return {
        'totalEmployees': 0,
        'averageSalary': 0.0,
        'totalSalaryBudget': 0.0,
        'departmentCounts': <String, int>{},
        'gradeCounts': <int, int>{},
      };
    }

    final departmentCounts = <String, int>{};
    final gradeCounts = <int, int>{};
    double totalSalary = 0;

    for (final employee in employees) {
      // عدد الموظفين حسب القسم
      departmentCounts[employee.department] =
          (departmentCounts[employee.department] ?? 0) + 1;

      // عدد الموظفين حسب الدرجة
      gradeCounts[employee.jobGrade] =
          (gradeCounts[employee.jobGrade] ?? 0) + 1;

      // إجمالي الرواتب
      totalSalary += employee.totalSalary;
    }

    return {
      'totalEmployees': employees.length,
      'averageSalary': totalSalary / employees.length,
      'totalSalaryBudget': totalSalary,
      'departmentCounts': departmentCounts,
      'gradeCounts': gradeCounts,
    };
  }

  // تصدير البيانات إلى JSON
  Future<String> exportToJson() async {
    final employees = await loadEmployees();
    final employeesJson = employees.map((e) => e.toJson()).toList();
    return jsonEncode(employeesJson);
  }

  // استيراد البيانات من JSON
  Future<bool> importFromJson(String jsonString) async {
    try {
      final employeesJson = jsonDecode(jsonString) as List;
      final employees = employeesJson
          .map((json) => Employee.fromJson(json))
          .toList();
      await saveEmployees(employees);
      return true;
    } catch (e) {
      print('خطأ في استيراد البيانات: $e');
      return false;
    }
  }

  // إنشاء نسخة احتياطية
  Future<void> createBackup() async {
    final employees = await loadEmployees();
    final backup = {
      'timestamp': DateTime.now().toIso8601String(),
      'employees': employees.map((e) => e.toJson()).toList(),
    };

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'backup_${DateTime.now().millisecondsSinceEpoch}',
      jsonEncode(backup),
    );
  }

  // استعادة من النسخة الاحتياطية
  Future<List<String>> getAvailableBackups() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    return keys.where((key) => key.startsWith('backup_')).toList();
  }

  Future<bool> restoreFromBackup(String backupKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final backupString = prefs.getString(backupKey);

      if (backupString == null) return false;

      final backup = jsonDecode(backupString);
      final employeesJson = backup['employees'] as List;
      final employees = employeesJson
          .map((json) => Employee.fromJson(json))
          .toList();

      await saveEmployees(employees);
      return true;
    } catch (e) {
      print('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    }
  }

  // مسح جميع البيانات
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_employeesKey);
  }

  // التحقق من وجود موظف بنفس الرقم الوطني
  Future<bool> isNationalIdExists(
    String nationalId, {
    String? excludeId,
  }) async {
    final employees = await loadEmployees();
    return employees.any(
      (e) => e.nationalId == nationalId && e.id != excludeId,
    );
  }

  // الحصول على رقم تسلسلي جديد للموظف
  Future<String> generateNewEmployeeId() async {
    final employees = await loadEmployees();
    final maxId = employees.isEmpty
        ? 0
        : employees
              .map((e) => int.tryParse(e.id) ?? 0)
              .reduce((a, b) => a > b ? a : b);
    return (maxId + 1).toString().padLeft(6, '0');
  }
}
